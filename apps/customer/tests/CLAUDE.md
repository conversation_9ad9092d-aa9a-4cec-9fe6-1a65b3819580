# Tests

This directory contains comprehensive Playwright tests for the Customer App

# Guidelines

## Best Practices for Writing Playwright Tests

Here are the recommended practices to follow when writing Playwright tests:

## Test Structure and Logic

**Write simple, linear tests** - Each test should follow a clear sequential path from start to finish, it should not
branch, it should not have try/catch, it should not fallback it should ignore failure states. A test either passes or
fails completely, it cannot partially pass.

GUIDANCE: Write tests that execute sequentially without conditional branches and without try/catch.

## Selector Strategy

**Use stable, semantic selectors** - Always use data attributes, accessible roles, or stable IDs for element selection.
IMPORTANT: Always use `data-testid` attributes. When these attributes are missing in the code being tested, add them to the source code.

GUIDANCE: USE `data-testid`

**Create reusable selector utilities** - Store selectors in helper functions within the apps/customer/tests/helpers/tests directory. Create new utility files in that directory when needed.

GUIDANCE: Create util functions

**Use <PERSON>wright's locator API** - Always use `page.locator()` for its auto-waiting capabilities and better error messages.

## Timing and Synchronization

**Wait for specific conditions** - Always wait for elements to be visible, enabled, or loaded before interacting with them. Use Playwright's built-in waiting mechanisms.

GUIDANCE: Use condition-based waits instead of time-based waits.

**Account for asynchronous operations** - Consider network requests, animations, and dynamic content loading in your tests.

## Test Design

**Keep tests focused and concise** - Each test should verify one specific functionality or user flow.

**Ensure test isolation** - Each test should run independently without relying on state from previous tests.

**Clean up test data** - Always remove test artifacts that could affect subsequent test runs.

## Authentication and Security

**Use Playwright's authentication features** - Leverage storage state or authentication helpers instead of logging in repeatedly.

**Store credentials securely** - Use environment variables or secure storage for usernames and passwords.

## Error Handling and Assertions

**Investigate all test failures** - Address flaky tests immediately and understand root causes.

**Provide meaningful error context** - Add descriptive test names and custom error messages to aid debugging.

**Use specific Playwright assertions** - Leverage Playwright's built-in matchers for clearer error messages.

## Test Structure

### Core Test Files

- **`report-system.spec.ts`** - Main functionality tests for report components, templates, and editor features
- **`document-templates.spec.ts`** - Tests for template management, selection, and document creation
- **`report-api.spec.ts`** - API endpoint tests for report generation and data fetching
- **`nested-component-loading.spec.ts`** - **NEW** - Comprehensive tests for nested components with charts and citations
- **`report-section-loading.spec.ts`** - Report section loading behavior and state management
- **`document-entity-run-selector.spec.ts`** - Entity and run selection functionality
- **`issues/issue-eko-*.spec.ts`** - per issue tests used to recreate an issue and test it has been fixed.

### Helper Files

- **`helpers/tests/test-utils.ts`** - Common utilities and helper functions for tests

### Test Commands

```bash
# Run specific test file
npx playwright test report-system.spec.ts

# Run the new nested component loading tests
npx playwright test nested-component-loading.spec.ts

# Run tests in specific browser
npx playwright test --project=chromium

```

# Test Configuration

Tests are configured in `playwright.config.ts` with:
- Base URL: `http://localhost:3333`

## Test Data and Setup

### Mock Data

Tests include mocking capabilities for:
- API responses
- Error conditions
- Network failures
- Large datasets

## Writing New Tests

### Using Test Utilities

```typescript
import { TestUtils } from '../helpers/tests/test-utils';

test('my test', async ({ page }) => {
  const testUtils = new TestUtils(page);
  
  // Login
  await testUtils.login();
  
  // Create document
  const documentId = await testUtils.createDocumentFromTemplate();
  
  // Add component
  await testUtils.addReportComponent('section', {
    id: 'my-section',
    title: 'My Section'
  });
  
  // Verify
  await testUtils.checkComponentExists('my-section');
});
```

### Best Practices

1. **Use descriptive test names** that explain what is being tested
2. **Group related tests** using `test.describe()`
3. **Use test utilities** for common operations
4. **Mock external dependencies** when testing error conditions
5. **Clean up after tests** (automatic with Playwright)
6. **Test both happy path and error cases**
7. **Include accessibility and mobile tests**
8. **Use data-testid attributes** - add `data-testid` attributes to the code base as needed

### Test Patterns

```typescript
// Component interaction pattern
await testUtils.openComponentConfig('.report-section');
await testUtils.fillComponentConfig({ title: 'New Title' });
await testUtils.confirmComponentConfig();

// API testing pattern
const response = await page.request.get('/api/endpoint');
expect(response.status()).toBe(200);
const data = await response.json();
expect(data).toHaveProperty('text');

// Error handling pattern
await testUtils.mockApiError('/api/endpoint');
await expect(page.locator('.error')).toBeVisible();
```

## Debugging Tests

### Visual Debugging

```bash
# Run with UI for visual debugging
npm run test:ui

# Run in headed mode to see browser
npm run test:headed

# Debug specific test
npx playwright test --debug report-system.spec.ts
```

### Screenshots and Videos

Playwright automatically captures:
- Screenshots on failure
- Videos of test runs (in CI)
- Traces for debugging

### Common Issues

1. **Timing Issues**: Use `waitFor` methods for synchronization
2. **Element Not Found**: Verify selectors and ensure elements are visible
3. **Authentication**: Ensure login flow works correctly
4. **API Mocking**: Verify mock responses match expected format

## Continuous Integration

Tests are designed to run in CI environments with:
- Headless browser execution
- Retry on failure
- Parallel execution disabled for stability
- Automatic browser installation

## Maintenance

### Updating Tests

When adding new features:
1. Add corresponding test cases
2. Update test utilities if needed
3. Verify all existing tests still pass
4. Update this file if test structure changes

### Performance Monitoring

Tests include performance checks for:
- Component loading times
- Large document handling
- API response times
- Memory usage patterns

Monitor test execution times and update timeouts as needed.

# Workflow

Follow this workflow when working with tests:

[ ] Examine the codebase to understand what the test is testing
[ ] Run playwright tests using: `cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts`
- For more detailed logging use: `DEBUG=pw:* cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts`
- Add console monitoring when needed:
```ts
  page.on('console', msg => {
  console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
  });
```
[ ] Determine whether the test or the code needs fixing
[ ] Create a plan for changes:
    - Fix the test if the test logic is incorrect
    - Fix the code if the functionality is broken
    - Consider increasing timeouts for operations that naturally take time (page loads, etc.)
    - Search the web or use Context7 MCP tool for library documentation when needed
[ ] Implement the changes using available tools:
    - Use `run_in_db.sh` for analytics database operations
    - Use `run_in_customer_db.sh` for customer database operations
    - Use `tsc --noEmit` to verify TypeScript code
[ ] Commit changes with descriptive comments including the test name
[ ] Create a PR and push changes when complete

---
Each test should follow a clear sequential path from start to finish, it should not branch, it should not have try/catch, it should not fallback it should ignore failure states. A test either passes or fails, it does not partially pass.
---
